/**
 * 通知服务工具类
 * 处理系统通知的创建和发送
 */

const { NotificationModel } = require('./database');

class NotificationService {
  /**
   * 创建通知
   * @param {Object} notificationData - 通知数据
   * @param {number} notificationData.user_id - 接收用户ID
   * @param {string} notificationData.type - 通知类型
   * @param {string} notificationData.title - 通知标题
   * @param {string} notificationData.content - 通知内容
   * @param {number} notificationData.related_id - 相关对象ID
   * @param {string} notificationData.related_type - 相关对象类型
   */
  static async createNotification(notificationData) {
    try {
      const result = await NotificationModel.create(notificationData);
      return result;
    } catch (error) {
      console.error('创建通知失败:', error);
      throw error;
    }
  }

  /**
   * 合同提交通知
   * 通知审核员有新的合同需要审核
   */
  static async notifyContractSubmitted(contractData, reviewerId) {
    const notificationData = {
      user_id: reviewerId,
      type: 'contract_submitted',
      title: '新合同待审核',
      content: `员工 ${contractData.submitter_name} 提交了合同 ${contractData.serial_number}，请及时审核`,
      related_id: contractData.id,
      related_type: 'contract'
    };

    return await this.createNotification(notificationData);
  }

  /**
   * 合同分配通知
   * 通知审核员合同已分配给他们
   */
  static async notifyContractAssigned(contractData, reviewerId) {
    const notificationData = {
      user_id: reviewerId,
      type: 'contract_assigned',
      title: '合同已分配',
      content: `合同 ${contractData.serial_number} 已分配给您审核，请及时处理`,
      related_id: contractData.id,
      related_type: 'contract'
    };

    return await this.createNotification(notificationData);
  }

  /**
   * 合同审核通过通知
   * 通知提交者合同已通过审核
   */
  static async notifyContractApproved(contractData, submitterId) {
    const notificationData = {
      user_id: submitterId,
      type: 'contract_approved',
      title: '合同审核通过',
      content: `您的合同 ${contractData.serial_number} 已审核通过`,
      related_id: contractData.id,
      related_type: 'contract'
    };

    return await this.createNotification(notificationData);
  }

  /**
   * 合同审核拒绝通知
   * 通知提交者合同被拒绝，包含拒绝原因
   */
  static async notifyContractRejected(contractData, submitterId, reason) {
    const notificationData = {
      user_id: submitterId,
      type: 'contract_rejected',
      title: '合同审核未通过',
      content: `您的合同 ${contractData.serial_number} 审核未通过。原因：${reason}`,
      related_id: contractData.id,
      related_type: 'contract'
    };

    return await this.createNotification(notificationData);
  }

  /**
   * 合同流转到市局通知
   * 通知提交者合同已通过县局审核，正在市局审核中
   */
  static async notifyContractTransferredToCity(contractData, submitterId) {
    const notificationData = {
      user_id: submitterId,
      type: 'contract_transferred',
      title: '合同已流转市局审核',
      content: `您的合同 ${contractData.serial_number} 已通过县局审核，正在市局审核中`,
      related_id: contractData.id,
      related_type: 'contract'
    };

    return await this.createNotification(notificationData);
  }

  /**
   * 合同编号分配完成通知
   * 通知提交者合同编号已分配完成
   */
  static async notifyContractNumberAssigned(contractData, submitterId, contractNumber) {
    const notificationData = {
      user_id: submitterId,
      type: 'contract_number_assigned',
      title: '合同编号分配完成',
      content: `您的合同 ${contractData.serial_number} 已完成编号分配，合同编号：${contractNumber}`,
      related_id: contractData.id,
      related_type: 'contract'
    };

    return await this.createNotification(notificationData);
  }

  /**
   * 合同流转到法规员通知
   * 通知法规员有新的合同需要分配编号
   */
  static async notifyContractAssignedToLegalOfficer(contractData, legalOfficerId) {
    const notificationData = {
      user_id: legalOfficerId,
      type: 'contract_assigned_to_legal_officer',
      title: '新合同待分配编号',
      content: `合同 ${contractData.serial_number} 已通过审核，请分配合同编号`,
      related_id: contractData.id,
      related_type: 'contract'
    };

    return await this.createNotification(notificationData);
  }

  /**
   * 系统通知
   * 发送系统级别的通知给指定用户
   */
  static async notifySystemMessage(userId, title, content) {
    const notificationData = {
      user_id: userId,
      type: 'system_notice',
      title: title,
      content: content,
      related_id: null,
      related_type: 'system'
    };

    return await this.createNotification(notificationData);
  }

  /**
   * 批量发送系统通知
   * 给多个用户发送相同的系统通知
   */
  static async notifySystemMessageBatch(userIds, title, content) {
    const promises = userIds.map(userId =>
      this.notifySystemMessage(userId, title, content)
    );

    try {
      const results = await Promise.all(promises);
      return results;
    } catch (error) {
      console.error('批量发送系统通知失败:', error);
      throw error;
    }
  }

  /**
   * 合同状态变更通知处理器
   * 根据合同状态变更自动发送相应通知
   */
  static async handleContractStatusChange(contractData, oldStatus, newStatus) {
    try {
      switch (newStatus) {
        case 'pending':
          // 合同提交或重新提交，通知审核员
          if (contractData.reviewer_id) {
            await this.notifyContractAssigned(contractData, contractData.reviewer_id);
          }
          break;

        case 'approved':
          // 合同审核通过，通知提交者
          await this.notifyContractApproved(contractData, contractData.submitter_id);
          break;

        case 'rejected':
          // 合同审核拒绝，通知提交者
          const reason = contractData.review_comment || '未提供具体原因';
          await this.notifyContractRejected(contractData, contractData.submitter_id, reason);
          break;

        default:
      }
    } catch (error) {
      console.error('处理合同状态变更通知失败:', error);
      // 不抛出错误，避免影响主业务流程
    }
  }

  /**
   * 获取用户未读通知数量
   */
  static async getUnreadCount(userId) {
    try {
      return await NotificationModel.getUnreadCount(userId);
    } catch (error) {
      console.error('获取未读通知数量失败:', error);
      return 0;
    }
  }

  /**
   * 清理过期通知
   * 定期清理30天前的已读通知
   */
  static async cleanupOldNotifications() {
    try {
      const result = await NotificationModel.cleanupOldNotifications();
      return result;
    } catch (error) {
      console.error('清理过期通知失败:', error);
      throw error;
    }
  }

  /**
   * 通知类型映射
   */
  static get NOTIFICATION_TYPES() {
    return {
      CONTRACT_SUBMITTED: 'contract_submitted',
      CONTRACT_APPROVED: 'contract_approved',
      CONTRACT_REJECTED: 'contract_rejected',
      CONTRACT_ASSIGNED: 'contract_assigned',
      CONTRACT_TRANSFERRED: 'contract_transferred',
      CONTRACT_NUMBER_ASSIGNED: 'contract_number_assigned',
      SYSTEM_NOTICE: 'system_notice'
    };
  }

  /**
   * 验证通知类型
   */
  static isValidNotificationType(type) {
    return Object.values(this.NOTIFICATION_TYPES).includes(type);
  }
}

module.exports = NotificationService;
